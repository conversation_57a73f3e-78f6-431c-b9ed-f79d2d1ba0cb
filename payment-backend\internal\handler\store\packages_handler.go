package store

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"

	"payment-backend/internal/domain"
	"payment-backend/internal/domain/store"
	"payment-backend/internal/logger"
	"payment-backend/internal/middleware"
)

// PackageHandler 流量包处理器
type PackageHandler struct {
	packageService store.PackageService
	logger         logger.Logger
}

// NewPackageHandler 创建流量包处理器
func NewPackageHandler(packageService store.PackageService, logger logger.Logger) *PackageHandler {
	return &PackageHandler{
		packageService: packageService,
		logger:         logger,
	}
}

// ListAllPackages 获取所有流量包（终端用户）
// @Summary 获取所有流量包
// @Description 获取所有可用的流量包列表，需要用户认证
// @Tags 流量包管理
// @Accept json
// @Produce json
// @Param x-user-id header string true "用户ID" example("user123")
// @Param x-role header string true "用户角色" example("customer")
// @Param currency query string false "货币单位" example("USD")
// @Param country query string false "国家" example("US")
// @Param limit query int false "每页数量，最小1，最大500" minimum(1) maximum(500) default(50)
// @Param offset query int false "偏移量，从0开始" minimum(0) default(0)
// @Success 200 {object} store.ListPackagesResponse "流量包列表"
// @Failure 400 {object} domain.ErrorResponse "请求参数错误, code=10001"
// @Failure 500 {object} domain.ErrorResponse "服务器内部错误, code=10003"
// @Router /api/v1/pay-service/store-service/packages [get]
func (h *PackageHandler) ListAllPackages(c *gin.Context) {
	// 获取用户上下文
	userContext, err := middleware.MustGetUserContext(c)
	if err != nil {
		h.logger.Warn("Invalid user context type", logger.Error(err))
		c.JSON(http.StatusBadRequest, domain.ErrorResponse{
			Code:    domain.ErrCodeInvalidRequest,
			Message: "Invalid user context",
		})
		return
	}

	// 解析查询参数
	var filter store.PackageFilter
	if currency := c.Query("currency"); currency != "" {
		filter.Currency = &currency
	}
	if country := c.Query("country"); country != "" {
		filter.Country = &country
	}

	// 解析分页参数
	pagination := &store.PaginationRequest{
		Limit:  50, // 默认值
		Offset: 0,  // 默认值
	}

	if limitStr := c.Query("limit"); limitStr != "" {
		if limit, err := strconv.Atoi(limitStr); err == nil && limit > 0 && limit <= 500 {
			pagination.Limit = limit
		}
	}

	if offsetStr := c.Query("offset"); offsetStr != "" {
		if offset, err := strconv.Atoi(offsetStr); err == nil && offset >= 0 {
			pagination.Offset = offset
		}
	}

	// 调用服务
	response, err := h.packageService.ListAllPackages(userContext, &filter, pagination)
	if err != nil {
		h.logger.Error("Failed to list packages", logger.Error(err))
		c.JSON(http.StatusInternalServerError, domain.ErrorResponse{
			Code:    domain.ErrCodeInternalError,
			Message: "Failed to list packages",
		})
		return
	}

	c.JSON(http.StatusOK, response)
}

// AdminAddPackages 添加流量包（管理员）
// @Summary 添加流量包
// @Description 添加新的流量包，需要管理员权限
// @Tags 流量包管理-管理员
// @Accept json
// @Produce json
// @Param request body store.CreatePackageRequest true "创建流量包请求"
// @Success 201 {object} map[string]string "创建成功"
// @Failure 400 {object} domain.ErrorResponse "请求参数错误, code=10001"
// @Failure 500 {object} domain.ErrorResponse "服务器内部错误, code=10003"
// @Router /api/v1/pay-service/admin/store-service/packages [put]
func (h *PackageHandler) AdminAddPackages(c *gin.Context) {
	var req store.CreatePackageRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn("Invalid request body", logger.Error(err))
		c.JSON(http.StatusBadRequest, domain.ErrorResponse{
			Code:    domain.ErrCodeInvalidRequest,
			Message: "Invalid request body: " + err.Error(),
		})
		return
	}

	// 设置默认值
	if req.Currency == "" {
		req.Currency = "USD"
	}
	if req.Country == "" {
		req.Country = "US"
	}

	// 调用服务
	if err := h.packageService.AdminAddPackages(&req); err != nil {
		h.logger.Error("Failed to create package", logger.Error(err))
		c.JSON(http.StatusInternalServerError, domain.ErrorResponse{
			Code:    domain.ErrCodeInternalError,
			Message: "Failed to create package",
		})
		return
	}

	c.JSON(http.StatusCreated, gin.H{"message": "Package created successfully"})
}

// AdminDeletePackages 删除流量包（管理员）
// @Summary 删除流量包
// @Description 删除指定的流量包，需要管理员权限
// @Tags 流量包管理-管理员
// @Accept json
// @Produce json
// @Param request body store.DeletePackageRequest true "删除流量包请求"
// @Success 200 {object} map[string]string "删除成功"
// @Failure 400 {object} domain.ErrorResponse "请求参数错误, code=10001"
// @Failure 500 {object} domain.ErrorResponse "服务器内部错误, code=10003"
// @Router /api/v1/pay-service/admin/store-service/packages [delete]
func (h *PackageHandler) AdminDeletePackages(c *gin.Context) {
	var req store.DeletePackageRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn("Invalid request body", logger.Error(err))
		c.JSON(http.StatusBadRequest, domain.ErrorResponse{
			Code:    domain.ErrCodeInvalidRequest,
			Message: "Invalid request body: " + err.Error(),
		})
		return
	}

	// 设置默认值
	if req.Currency == "" {
		req.Currency = "USD"
	}
	if req.Country == "" {
		req.Country = "US"
	}

	// 调用服务
	if err := h.packageService.AdminDeletePackages(&req); err != nil {
		h.logger.Error("Failed to delete package", logger.Error(err))
		c.JSON(http.StatusInternalServerError, domain.ErrorResponse{
			Code:    domain.ErrCodeInternalError,
			Message: "Failed to delete package",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Package deleted successfully"})
}

// AdminUpdatePackages 更新流量包（管理员）
// @Summary 更新流量包
// @Description 更新指定的流量包信息，需要管理员权限
// @Tags 流量包管理-管理员
// @Accept json
// @Produce json
// @Param request body store.UpdatePackageRequest true "更新流量包请求"
// @Success 200 {object} map[string]string "更新成功"
// @Failure 400 {object} domain.ErrorResponse "请求参数错误, code=10001"
// @Failure 500 {object} domain.ErrorResponse "服务器内部错误, code=10003"
// @Router /api/v1/pay-service/admin/store-service/packages [post]
func (h *PackageHandler) AdminUpdatePackages(c *gin.Context) {
	var req store.UpdatePackageRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn("Invalid request body", logger.Error(err))
		c.JSON(http.StatusBadRequest, domain.ErrorResponse{
			Code:    domain.ErrCodeInvalidRequest,
			Message: "Invalid request body: " + err.Error(),
		})
		return
	}

	// 调用服务
	if err := h.packageService.AdminUpdatePackages(&req); err != nil {
		h.logger.Error("Failed to update package", logger.Error(err))
		c.JSON(http.StatusInternalServerError, domain.ErrorResponse{
			Code:    domain.ErrCodeInternalError,
			Message: "Failed to update package",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Package updated successfully"})
}

// AdminListAllPackages 获取所有流量包（管理员）
// @Summary 获取所有流量包（管理员）
// @Description 获取所有流量包列表，包含完整信息，需要管理员权限
// @Tags 流量包管理-管理员
// @Accept json
// @Produce json
// @Param currency query string false "货币单位" example("USD")
// @Param country query string false "国家" example("US")
// @Param limit query int false "每页数量，最小1，最大500" minimum(1) maximum(500) default(50)
// @Param offset query int false "偏移量，从0开始" minimum(0) default(0)
// @Success 200 {object} store.AdminListPackagesResponse "流量包列表"
// @Failure 400 {object} domain.ErrorResponse "请求参数错误, code=10001"
// @Failure 500 {object} domain.ErrorResponse "服务器内部错误, code=10003"
// @Router /api/v1/pay-service/admin/store-service/packages [get]
func (h *PackageHandler) AdminListAllPackages(c *gin.Context) {
	// 解析查询参数
	var filter store.PackageFilter
	if currency := c.Query("currency"); currency != "" {
		filter.Currency = &currency
	}
	if country := c.Query("country"); country != "" {
		filter.Country = &country
	}

	// 解析分页参数
	pagination := &store.PaginationRequest{
		Limit:  50, // 默认值
		Offset: 0,  // 默认值
	}

	if limitStr := c.Query("limit"); limitStr != "" {
		if limit, err := strconv.Atoi(limitStr); err == nil && limit > 0 && limit <= 500 {
			pagination.Limit = limit
		}
	}

	if offsetStr := c.Query("offset"); offsetStr != "" {
		if offset, err := strconv.Atoi(offsetStr); err == nil && offset >= 0 {
			pagination.Offset = offset
		}
	}

	// 调用服务
	response, err := h.packageService.AdminListAllPackages(&filter, pagination)
	if err != nil {
		h.logger.Error("Failed to list packages for admin", logger.Error(err))
		c.JSON(http.StatusInternalServerError, domain.ErrorResponse{
			Code:    domain.ErrCodeInternalError,
			Message: "Failed to list packages",
		})
		return
	}

	c.JSON(http.StatusOK, response)
}
