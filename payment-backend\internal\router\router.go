package router

import (
	"github.com/gin-gonic/gin"

	"payment-backend/internal/config"
	"payment-backend/internal/handler"
	storeHandler "payment-backend/internal/handler/store"
	"payment-backend/internal/logger"
	"payment-backend/internal/middleware"

	// docs are generated by Swag CLI, you have to import it.
	docs "payment-backend/docs/api"

	swaggerfiles "github.com/swaggo/files"     // swagger embed files
	ginSwagger "github.com/swaggo/gin-swagger" // gin-swagger middleware
)

// SetupRoutes 设置路由
func SetupRoutes(
	router *gin.Engine,
	orderHandler *handler.OrderHandler,
	storeHandler *storeHandler.PackageHandler,
	logger logger.Logger,
	config *config.Config,
) *gin.Engine {

	// 全局中间件
	router.Use(gin.Logger())
	router.Use(gin.Recovery())

	// 健康检查（无需认证）
	router.GET("/health", orderHandler.HealthCheck)

	if gin.Mode() == gin.DebugMode {

		docs.SwaggerInfo.BasePath = "/api/v1" // 设置路径
		router.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerfiles.Handler))
	}

	// API v1 路由组
	// 不需要校验 jwt:
	// /api/v1/pay-service/order-service/webhooks/*
	// 其余都需要校验 jwt:
	// /api/v1/pay-service/order-service/*
	v1 := router.Group("/api/v1/pay-service")
	{
		// 订单服务路由组
		orderService := v1.Group("/order-service")
		{
			// 需要认证的路由
			authenticated := orderService.Group("")
			authenticated.Use(middleware.AuthMiddleware(logger))
			authenticated.Use(middleware.RequireRequestIDMiddleware())
			{
				// 订单管理 - 需要认证
				authenticated.POST("/orders", orderHandler.CreateOrder)
				// authenticated.GET("/orders/:order_id", orderHandler.GetOrder)
				// authenticated.GET("/orders/id/:id", orderHandler.GetOrderByID)
				authenticated.GET("/orders", orderHandler.GetUserOrders)
				// authenticated.PUT("/orders/:order_id", orderHandler.UpdateOrder)
				// authenticated.POST("/orders/:order_id/cancel", orderHandler.CancelOrder)
				// authenticated.POST("/orders/:order_id/refund", orderHandler.RefundOrder)
			}

			// Webhook - 可选认证（外部调用）
			webhooks := orderService.Group("/webhooks")
			webhooks.Use(middleware.OptionalAuthMiddleware(logger))
			{
				webhooks.POST("/stripe", orderHandler.ProcessWebhookStripe)
			}
		}

		// 流量包终端用户访问接口
		packageService := v1.Group("/store-service")
		{
			authenticated := packageService.Group("")
			authenticated.Use(middleware.AuthMiddleware(logger))
			authenticated.Use(middleware.RequireRequestIDMiddleware())
			authenticated.GET("/packages", storeHandler.ListAllPackages)
		}

		// 管理员路由 - 需要管理员权限
		admin := v1.Group("/admin")
		admin.Use(middleware.OptionalAuthMiddleware(logger)) // 暂时可选
		// admin.Use(middleware.RequireRoleFromConfig(config.Admin.AllowedRoles))
		{
			adminOrderService := admin.Group("/order-service")
			{
				// 这里可以添加管理员专用的接口
				adminOrderService.GET("/orders", orderHandler.ListAllOrders)
				adminOrderService.POST("/orders/:order_id/force-refund", orderHandler.ForceRefund)
			}

			// 流量包运营人员管理接口
			adminPackageService := admin.Group("/store-service")
			{
				adminPackageService.PUT("/packages", storeHandler.AdminAddPackages)
				adminPackageService.DELETE("/packages", storeHandler.AdminDeletePackages)
				adminPackageService.POST("/packages", storeHandler.AdminUpdatePackages)
				adminPackageService.GET("/packages", storeHandler.AdminListAllPackages)
			}
		}
	}

	return router
}

// SetupTestRoutes 设置测试路由（用于单元测试）
func SetupTestRoutes(orderHandler *handler.OrderHandler) *gin.Engine {
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// 健康检查
	router.GET("/health", orderHandler.HealthCheck)

	// 测试路由（不使用中间件）
	router.POST("/orders", orderHandler.CreateOrder)
	router.GET("/orders/:order_id", orderHandler.GetOrder)
	router.GET("/orders/id/:id", orderHandler.GetOrderByID)
	router.GET("/orders", orderHandler.GetUserOrders)
	router.PUT("/orders/:order_id", orderHandler.UpdateOrder)
	router.POST("/orders/:order_id/cancel", orderHandler.CancelOrder)
	// router.POST("/orders/:order_id/refund", orderHandler.RefundOrder)
	router.POST("/webhooks/stripe", orderHandler.ProcessWebhookStripe)

	return router
}
