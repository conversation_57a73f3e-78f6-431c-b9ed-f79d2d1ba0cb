package store

import (
	"fmt"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"

	"payment-backend/internal/domain/store"
)

// PackageModel 流量包数据库模型
type PackageModel struct {
	ID              uint64     `gorm:"primaryKey;autoIncrement" json:"id"`
	PackageID       string     `gorm:"type:varchar(128);not null;uniqueIndex:idx_packages_package_id" json:"package_id"`
	PackageName     string     `gorm:"type:varchar(128);not null" json:"package_name"`
	PackageDesc     string     `gorm:"type:varchar(1024);not null" json:"package_desc"`
	Entitlement     string     `gorm:"type:varchar(128);not null" json:"entitlement"`
	OriginalPrice   string     `gorm:"type:decimal(18,2);not null" json:"original_price"`
	DiscountPrice   *string    `gorm:"type:decimal(18,2)" json:"discount_price"`
	DiscountPercent *int32     `gorm:"type:int" json:"discount_percent"`
	Currency        string     `gorm:"type:char(3);not null;index:idx_packages_currency" json:"currency"`
	Country         string     `gorm:"type:varchar(64);not null;index:idx_packages_country" json:"country"`
	Extra1          string     `gorm:"type:varchar(512)" json:"extra1"`
	Extra2          string     `gorm:"type:varchar(512)" json:"extra2"`
	Extra3          *int32     `gorm:"type:int" json:"extra3"`
	Extra4          *int32     `gorm:"type:int" json:"extra4"`
	CreatedAt       *time.Time `gorm:"not null;index:idx_packages_created_at" json:"created_at"`
	UpdatedAt       *time.Time `gorm:"not null" json:"updated_at"`
	Deleted         uint8      `gorm:"not null;default:0;index:idx_packages_deleted" json:"deleted"`
	DeletedAt       *time.Time `json:"deleted_at"`
}

// TableName 指定表名
func (PackageModel) TableName() string {
	return "store_packages"
}

// BeforeCreate GORM钩子，在创建前执行
func (p *PackageModel) BeforeCreate(tx *gorm.DB) error {
	now := time.Now()
	p.CreatedAt = &now
	p.UpdatedAt = &now

	// 如果没有设置PackageID，则自动生成UUID
	if p.PackageID == "" {
		p.PackageID = uuid.New().String()
	}

	return nil
}

// BeforeUpdate GORM钩子，在更新前执行
func (p *PackageModel) BeforeUpdate(tx *gorm.DB) error {
	now := time.Now()
	p.UpdatedAt = &now
	return nil
}

// ToDomain 转换为domain模型
func (p *PackageModel) ToDomain() (*store.Package, error) {
	originalPrice, err := parseDecimal(p.OriginalPrice)
	if err != nil {
		return nil, fmt.Errorf("failed to parse original price: %w", err)
	}

	var discountPrice *float64
	if p.DiscountPrice != nil {
		dp, err := parseDecimal(*p.DiscountPrice)
		if err != nil {
			return nil, fmt.Errorf("failed to parse discount price: %w", err)
		}
		discountPrice = &dp
	}

	return &store.Package{
		ID:              p.ID,
		PackageID:       p.PackageID,
		PackageName:     p.PackageName,
		PackageDesc:     p.PackageDesc,
		Entitlement:     p.Entitlement,
		OriginalPrice:   originalPrice,
		DiscountPrice:   discountPrice,
		DiscountPercent: p.DiscountPercent,
		Currency:        p.Currency,
		Country:         p.Country,
		Extra1:          p.Extra1,
		Extra2:          p.Extra2,
		Extra3:          p.Extra3,
		Extra4:          p.Extra4,
		CreatedAt:       p.CreatedAt,
		UpdatedAt:       p.UpdatedAt,
		Deleted:         p.Deleted > 0,
		DeletedAt:       p.DeletedAt,
	}, nil
}

// FromDomain 从domain模型创建
func (p *PackageModel) FromDomain(pkg *store.Package) {
	p.ID = pkg.ID
	p.PackageID = pkg.PackageID
	p.PackageName = pkg.PackageName
	p.PackageDesc = pkg.PackageDesc
	p.Entitlement = pkg.Entitlement
	p.OriginalPrice = fmt.Sprintf("%.2f", pkg.OriginalPrice)
	if pkg.DiscountPrice != nil {
		discountPriceStr := fmt.Sprintf("%.2f", *pkg.DiscountPrice)
		p.DiscountPrice = &discountPriceStr
	}
	p.DiscountPercent = pkg.DiscountPercent
	p.Currency = pkg.Currency
	p.Country = pkg.Country
	p.Extra1 = pkg.Extra1
	p.Extra2 = pkg.Extra2
	p.Extra3 = pkg.Extra3
	p.Extra4 = pkg.Extra4
	p.CreatedAt = pkg.CreatedAt
	p.UpdatedAt = pkg.UpdatedAt
	if pkg.Deleted {
		p.Deleted = 1
	} else {
		p.Deleted = 0
	}
	p.DeletedAt = pkg.DeletedAt
}

// parseDecimal 解析decimal字符串为float64
func parseDecimal(s string) (float64, error) {
	var f float64
	_, err := fmt.Sscanf(s, "%f", &f)
	return f, err
}

// AutoMigrate 自动迁移数据库表结构
func AutoMigrate(db *gorm.DB) error {
	return db.AutoMigrate(
		&PackageModel{},
	)
}
